import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:nsl/providers/tab_state_provider.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class DynamicTabHeader extends StatelessWidget {
  const DynamicTabHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TabStateProvider>(
      builder: (context, tabProvider, child) {
        if (!tabProvider.hasActiveTabs) {
          return const SizedBox.shrink();
        }

        return Container(
          height: 28,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(color: Color(0xffD0D0D0), width: .5),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: tabProvider.tabs.map((tab) {
                      final isActive = tabProvider.activeTab?.id == tab.id;
                      return _buildTabItem(context, tab, isActive, tabProvider);
                    }).toList(),
                  ),
                ),
              ),
              // Tab actions (close all, etc.)
              _buildTabActions(context, tabProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTabItem(BuildContext context, TabState tab, bool isActive,
      TabStateProvider tabProvider) {
    return Container(
      height: 28,
      constraints: const BoxConstraints(
        minWidth: 120,
        maxWidth: 200,
      ),
      decoration: BoxDecoration(
        color: isActive ? Colors.black : Colors.white,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => tabProvider.switchToTab(tab.id),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            child: Row(
              children: [
                // Tab title
                Expanded(
                  child: Text(
                    tab.title,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: isActive ? Colors.white : Colors.black,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Unsaved changes indicator
                if (tab.hasUnsavedChanges || tab.hasWorkInProgress())
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(left: 4, right: 4),
                    decoration: const BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),

                // Close button
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => _handleTabClose(context, tab, tabProvider),
                    child: Container(
                      width: 24, // Adjust size as needed
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        border: Border.all(color: Colors.white, width: .5),
                        // borderRadius:
                        //     BorderRadius.circular(4), // Rounded corners
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.close,
                        size: 14,
                        color: isActive ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabActions(BuildContext context, TabStateProvider tabProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          // Close all tabs button
          if (tabProvider.tabCount > 1)
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () => _handleCloseAllTabs(context, tabProvider),
                child: Tooltip(
                  message: 'Close all tabs',
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      Icons.close_fullscreen,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _handleTabClose(
      BuildContext context, TabState tab, TabStateProvider tabProvider) {
    // Try to close the tab
    final closed = tabProvider.closeTab(tab.id);

    if (!closed) {
      // Tab has unsaved changes, show confirmation dialog
      _showUnsavedChangesDialog(context, tab, tabProvider);
    }
  }

  void _handleCloseAllTabs(BuildContext context, TabStateProvider tabProvider) {
    final tabsWithChanges = tabProvider.getTabsWithUnsavedChanges();

    if (tabsWithChanges.isNotEmpty) {
      _showCloseAllTabsDialog(context, tabProvider, tabsWithChanges);
    } else {
      tabProvider.closeAllTabs();
    }
  }

  void _showUnsavedChangesDialog(
      BuildContext context, TabState tab, TabStateProvider tabProvider) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Unsaved Changes',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Tab "${tab.title}" has unsaved changes. Do you want to close it anyway?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
              ),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                tabProvider.forceCloseTab(tab.id);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Close Anyway',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showCloseAllTabsDialog(BuildContext context,
      TabStateProvider tabProvider, List<TabState> tabsWithChanges) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Close All Tabs',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${tabsWithChanges.length} tab(s) have unsaved changes:',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              ...tabsWithChanges.map((tab) => Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 4),
                    child: Text(
                      '• ${tab.title}',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey[700],
                      ),
                    ),
                  )),
              const SizedBox(height: 8),
              Text(
                'Do you want to close all tabs anyway?',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
              ),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                tabProvider.closeAllTabs();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Close All',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
