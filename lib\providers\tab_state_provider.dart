import 'package:flutter/foundation.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/utils/logger.dart';

/// Represents a single tab's state
class TabState {
  final String id;
  final String title;
  final String type; // 'go', 'role', 'object'
  final Map<String, dynamic> originalData;

  // GO-specific state
  GoModel? goModel;
  Map<int, String?> loFunctionTypes = {};
  Map<int, PostgresRole?> loSelectedRoles = {};
  Map<int, String?> loExecutionRights = {};
  Map<int, List<PostgresRole?>> loMultipleRoles = {};
  Map<int, List<String?>> loMultipleExecutionRights = {};
  Map<int, List<SelectedObjectData>> loSelectedObjectsList = {};
  Map<int, bool> pathwayCreationStates = {};
  Map<int, PostgresRole?> pathwaySelectedRoles = {};
  Map<int, String?> pathwaySelectedTypes = {};
  Map<int, String?> pathwaySelectedLOs = {};
  Map<int, List<PathwayEntry>> pathwayEntries = {};
  Map<int, bool> loInsertionStates = {};
  Map<int, bool> loRolesSectionExpanded = {};

  // UI state
  bool showLocalObjectiveDetails = false;
  int? selectedLocalObjectiveIndex;
  bool isValidating = false;
  String? validationError;
  bool isGoValidateVisible = false;

  // Unsaved changes tracking
  bool hasUnsavedChanges = false;
  DateTime lastModified = DateTime.now();

  TabState({
    required this.id,
    required this.title,
    required this.type,
    required this.originalData,
  });

  /// Create a unique ID for a tab based on type and data
  static String generateId(String type, Map<String, dynamic> data) {
    switch (type) {
      case 'go':
        return 'go_${data['goId'] ?? data['name'] ?? DateTime.now().millisecondsSinceEpoch}';
      case 'role':
        return 'role_${data['roleId'] ?? data['name'] ?? DateTime.now().millisecondsSinceEpoch}';
      case 'object':
        return 'object_${data['entityId'] ?? data['name'] ?? DateTime.now().millisecondsSinceEpoch}';
      default:
        return '${type}_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Mark tab as having unsaved changes
  void markAsModified() {
    hasUnsavedChanges = true;
    lastModified = DateTime.now();
  }

  /// Mark tab as saved
  void markAsSaved() {
    hasUnsavedChanges = false;
  }

  /// Check if tab has meaningful work in progress
  bool hasWorkInProgress() {
    if (type == 'go' && goModel != null) {
      // Only consider it work in progress if user has made actual modifications
      // beyond the initial data loaded from the library

      // Check if any LO has user configuration (this indicates actual work)
      if (loFunctionTypes.isNotEmpty ||
          loSelectedRoles.isNotEmpty ||
          loSelectedObjectsList.values.any((list) => list.isNotEmpty)) {
        return true;
      }

      // For newly created tabs from library data, don't consider them as having work in progress
      // Only consider it work in progress if there are actual user interactions
      // The above checks for LO configurations are sufficient to detect real user work
    }

    return false;
  }

  /// Helper method to compare two lists for equality
  bool _listsEqual<T>(List<T> list1, List<T> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }
}

/// Manages multiple tabs with individual state preservation
class TabStateProvider extends ChangeNotifier {
  final List<TabState> _tabs = [];
  TabState? _activeTab;

  // Getters
  List<TabState> get tabs => List.unmodifiable(_tabs);
  TabState? get activeTab => _activeTab;
  int get tabCount => _tabs.length;
  bool get hasActiveTabs => _tabs.isNotEmpty;

  /// Get tab by ID
  TabState? getTabById(String id) {
    try {
      return _tabs.firstWhere((tab) => tab.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get tab index by ID
  int getTabIndexById(String id) {
    return _tabs.indexWhere((tab) => tab.id == id);
  }

  /// Add or switch to a tab
  TabState addOrSwitchToTab(
      String type, String title, Map<String, dynamic> data) {
    final id = TabState.generateId(type, data);

    // Check if tab already exists
    TabState? existingTab = getTabById(id);

    if (existingTab != null) {
      // Switch to existing tab
      _activeTab = existingTab;
      Logger.info('TabStateProvider: Switched to existing tab: $title');
    } else {
      // Create new tab
      final newTab = TabState(
        id: id,
        title: title,
        type: type,
        originalData: Map<String, dynamic>.from(data),
      );

      // Initialize tab based on type
      _initializeTab(newTab, data);

      _tabs.add(newTab);
      _activeTab = newTab;

      Logger.info('TabStateProvider: Created new tab: $title (ID: $id)');
    }

    notifyListeners();
    return _activeTab!;
  }

  /// Initialize tab with data based on type
  void _initializeTab(TabState tab, Map<String, dynamic> data) {
    switch (tab.type) {
      case 'go':
        _initializeGoTab(tab, data);
        break;
      case 'role':
        _initializeRoleTab(tab, data);
        break;
      case 'object':
        _initializeObjectTab(tab, data);
        break;
    }
  }

  /// Initialize GO tab with GO data
  void _initializeGoTab(TabState tab, Map<String, dynamic> goData) {
    // Create GoModel from GO data
    final globalObjectives = GlobalObjectives(
      name: goData['name'] ?? '',
      description: goData['description'] ?? '',
      naturalLanguage: goData['name'] ?? '',
      roleType: '',
      version: "1.0",
      status: "Active",
      classification: "Process",
      goId: goData['goId'] ?? '',
      isValidated: true,
    );

    // Create LocalObjectivesList from the GO's local objectives
    final localObjectivesList = <LocalObjectivesList>[];
    final localObjectives = goData['localObjectives'] as List<String>? ?? [];

    for (int i = 0; i < localObjectives.length; i++) {
      final loName = localObjectives[i];
      if (loName.isNotEmpty) {
        localObjectivesList.add(LocalObjectivesList(
          loNumber: i + 1,
          name: loName,
          version: "1.0",
          status: "Active",
          naturalLanguage: loName,
          goId: goData['goId'] ?? '',
          loId: "lo_${i + 1}_${DateTime.now().millisecondsSinceEpoch}",
          roleType: null,
          terminal: false,
          pathwayData: null,
        ));
      }
    }

    // Create and set the GoModel
    tab.goModel = GoModel(
      globalObjectives: globalObjectives,
      localObjectivesList: localObjectivesList,
    );

    // Initialize LO-specific state maps
    for (int i = 0; i < localObjectives.length; i++) {
      tab.loSelectedObjectsList[i] = [];
      tab.loRolesSectionExpanded[i] = true;
    }

    tab.isGoValidateVisible = false;

    Logger.info(
        'TabStateProvider: Initialized GO tab with ${localObjectives.length} local objectives');
  }

  /// Initialize Role tab with role data
  void _initializeRoleTab(TabState tab, Map<String, dynamic> roleData) {
    // Role-specific initialization can be added here
    Logger.info('TabStateProvider: Initialized Role tab: ${roleData['name']}');
  }

  /// Initialize Object tab with object data
  void _initializeObjectTab(TabState tab, Map<String, dynamic> objectData) {
    // Object-specific initialization can be added here
    Logger.info(
        'TabStateProvider: Initialized Object tab: ${objectData['name']}');
  }

  /// Switch to tab by ID
  void switchToTab(String id) {
    final tab = getTabById(id);
    if (tab != null && tab != _activeTab) {
      _activeTab = tab;
      Logger.info('TabStateProvider: Switched to tab: ${tab.title}');
      notifyListeners();
    }
  }

  /// Switch to tab by index
  void switchToTabByIndex(int index) {
    if (index >= 0 && index < _tabs.length) {
      _activeTab = _tabs[index];
      Logger.info(
          'TabStateProvider: Switched to tab by index: $index (${_activeTab!.title})');
      notifyListeners();
    }
  }

  /// Close tab by ID
  bool closeTab(String id) {
    final tabIndex = getTabIndexById(id);
    if (tabIndex == -1) return false;

    final tab = _tabs[tabIndex];

    // Check for unsaved changes
    if (tab.hasUnsavedChanges || tab.hasWorkInProgress()) {
      // Return false to indicate unsaved changes - caller should handle confirmation
      return false;
    }

    _tabs.removeAt(tabIndex);

    // Update active tab if necessary
    if (_activeTab == tab) {
      if (_tabs.isNotEmpty) {
        // Switch to the tab to the left, or the first tab if we closed the first one
        final newIndex = tabIndex > 0 ? tabIndex - 1 : 0;
        _activeTab = _tabs[newIndex];
      } else {
        _activeTab = null;
      }
    }

    Logger.info('TabStateProvider: Closed tab: ${tab.title}');
    notifyListeners();
    return true;
  }

  /// Force close tab (ignoring unsaved changes)
  void forceCloseTab(String id) {
    final tabIndex = getTabIndexById(id);
    if (tabIndex == -1) return;

    final tab = _tabs[tabIndex];
    _tabs.removeAt(tabIndex);

    // Update active tab if necessary
    if (_activeTab == tab) {
      if (_tabs.isNotEmpty) {
        final newIndex = tabIndex > 0 ? tabIndex - 1 : 0;
        _activeTab = _tabs[newIndex];
      } else {
        _activeTab = null;
      }
    }

    Logger.info('TabStateProvider: Force closed tab: ${tab.title}');
    notifyListeners();
  }

  /// Close all tabs
  void closeAllTabs() {
    _tabs.clear();
    _activeTab = null;
    Logger.info('TabStateProvider: Closed all tabs');
    notifyListeners();
  }

  /// Get tabs with unsaved changes
  List<TabState> getTabsWithUnsavedChanges() {
    return _tabs
        .where((tab) => tab.hasUnsavedChanges || tab.hasWorkInProgress())
        .toList();
  }

  /// Mark active tab as modified
  void markActiveTabAsModified() {
    if (_activeTab != null) {
      _activeTab!.markAsModified();
      notifyListeners();
    }
  }

  /// Mark active tab as saved
  void markActiveTabAsSaved() {
    if (_activeTab != null) {
      _activeTab!.markAsSaved();
      notifyListeners();
    }
  }

  /// Update active tab's GO model
  void updateActiveTabGoModel(GoModel goModel) {
    if (_activeTab?.type == 'go') {
      _activeTab!.goModel = goModel;
      _activeTab!.markAsModified();
      notifyListeners();
    }
  }

  /// Get active tab's GO model
  GoModel? getActiveTabGoModel() {
    return _activeTab?.type == 'go' ? _activeTab!.goModel : null;
  }

  /// Update active tab's LO function type
  void setActiveTabLoFunctionType(int loIndex, String? functionType) {
    if (_activeTab?.type == 'go') {
      _activeTab!.loFunctionTypes[loIndex] = functionType;
      _activeTab!.markAsModified();
      notifyListeners();
    }
  }

  /// Get active tab's LO function type
  String? getActiveTabLoFunctionType(int loIndex) {
    return _activeTab?.type == 'go'
        ? _activeTab!.loFunctionTypes[loIndex]
        : null;
  }

  /// Update active tab's LO selected role
  void setActiveTabLoSelectedRole(int loIndex, PostgresRole? role) {
    if (_activeTab?.type == 'go') {
      _activeTab!.loSelectedRoles[loIndex] = role;
      _activeTab!.markAsModified();
      notifyListeners();
    }
  }

  /// Get active tab's LO selected role
  PostgresRole? getActiveTabLoSelectedRole(int loIndex) {
    return _activeTab?.type == 'go'
        ? _activeTab!.loSelectedRoles[loIndex]
        : null;
  }

  /// Update active tab's LO execution rights
  void setActiveTabLoExecutionRights(int loIndex, String? executionRights) {
    if (_activeTab?.type == 'go') {
      _activeTab!.loExecutionRights[loIndex] = executionRights;
      _activeTab!.markAsModified();
      notifyListeners();
    }
  }

  /// Get active tab's LO execution rights
  String? getActiveTabLoExecutionRights(int loIndex) {
    return _activeTab?.type == 'go'
        ? _activeTab!.loExecutionRights[loIndex]
        : null;
  }

  /// Add selected object to active tab's LO
  void addActiveTabLoSelectedObject(
      int loIndex, Map<String, dynamic> object, List<String> attributes) {
    if (_activeTab?.type == 'go') {
      if (_activeTab!.loSelectedObjectsList[loIndex] == null) {
        _activeTab!.loSelectedObjectsList[loIndex] = [];
      }

      final id = SelectedObjectData.generateId(object);
      final selectedObjectData = SelectedObjectData(
        object: object,
        attributes: attributes,
        id: id,
      );

      _activeTab!.loSelectedObjectsList[loIndex]!.add(selectedObjectData);
      _activeTab!.markAsModified();
      notifyListeners();
    }
  }

  /// Get active tab's LO selected objects
  List<SelectedObjectData> getActiveTabLoSelectedObjects(int loIndex) {
    return _activeTab?.type == 'go'
        ? (_activeTab!.loSelectedObjectsList[loIndex] ?? [])
        : [];
  }

  /// Set active tab's local objective details visibility
  void setActiveTabShowLocalObjectiveDetails(int loIndex) {
    if (_activeTab?.type == 'go') {
      _activeTab!.showLocalObjectiveDetails = true;
      _activeTab!.selectedLocalObjectiveIndex = loIndex;
      notifyListeners();
    }
  }

  /// Hide active tab's local objective details
  void hideActiveTabLocalObjectiveDetails() {
    if (_activeTab?.type == 'go') {
      _activeTab!.showLocalObjectiveDetails = false;
      _activeTab!.selectedLocalObjectiveIndex = null;
      notifyListeners();
    }
  }

  /// Get active tab's selected local objective index
  int? getActiveTabSelectedLocalObjectiveIndex() {
    return _activeTab?.type == 'go'
        ? _activeTab!.selectedLocalObjectiveIndex
        : null;
  }

  /// Check if active tab shows local objective details
  bool getActiveTabShowLocalObjectiveDetails() {
    return _activeTab?.type == 'go'
        ? _activeTab!.showLocalObjectiveDetails
        : false;
  }
}
